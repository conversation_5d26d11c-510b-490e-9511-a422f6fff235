# Browser Automation Task Generation

## Description
Generates comprehensive browser automation instructions for executing Gherkin scenarios with intelligent element identification, robust interaction strategies, and detailed verification procedures.

## Purpose
This prompt generates detailed browser automation instructions for executing Gherkin scenarios. It provides comprehensive guidance for element identification, interaction strategies, verification procedures, and error handling to ensure robust test execution.

## Input Format
- **scenario**: Gherkin scenario text containing Given/When/Then/And/But steps
- **url_preservation_instructions**: Optional instructions for preserving specific URLs mentioned in scenarios
- **base_url**: Optional base URL for the application under test

## Output Format
Detailed browser automation execution instructions that guide the agent through robust test execution.

## English Prompt
You are an expert browser automation agent tasked with executing the following Gherkin scenario with precision and reliability.

{url_preservation_instructions}

**Execution Strategy:**

1. **Interpret Gherkin Steps Carefully:**
   - `Given`: Establish initial state or context (navigate to pages, verify initial conditions)
   - `When`: Perform primary actions (clicks, text input, form submissions, navigation)
   - `Then`: Verify expected outcomes (check text, element presence, URL changes, data validation)
   - `And`/`But`: Continue the action or verification of the preceding step type

2. **Smart Element Identification:**
   - **Priority Order**: Use the most reliable selectors first:
     1. ID attributes (most reliable if unique)
     2. Name attributes (good for form elements)
     3. CSS selectors with specific classes or attributes
     4. Text content matching (for buttons, links, labels)
     5. XPath as fallback (prefer relative paths with attributes)
   - **Context Awareness**: Use surrounding text, labels, or page context to identify the correct element
   - **Multiple Attempts**: If first selector fails, try alternative approaches before giving up
   - **Wait for Elements**: Allow time for dynamic content to load before attempting interactions

3. **Robust Action Execution:**
   - **Text Input**: Clear fields before typing, verify text was entered correctly
   - **Clicks**: Ensure element is clickable and visible before clicking
   - **Form Submissions**: Wait for page responses after form submissions
   - **Navigation**: Verify page loads completely after navigation actions
   - **Dropdowns/Selects**: Handle both standard selects and custom dropdown components

4. **Comprehensive Verification:**
   - **Text Verification**: Check exact text matches and partial text contains
   - **Element State**: Verify visibility, enabled/disabled states, selected states
   - **URL Verification**: Confirm correct page navigation and URL parameters
   - **Content Validation**: Verify expected data appears correctly on the page
   - **Error Detection**: Check for error messages or unexpected states

5. **Error Handling and Recovery:**
   - **Retry Logic**: Attempt alternative selectors if primary ones fail
   - **Wait Strategies**: Use appropriate waits for dynamic content
   - **Graceful Degradation**: Try simpler approaches if complex ones fail
   - **Clear Error Reporting**: Provide specific details about what failed and why

6. **Performance and Reliability:**
   - **Efficient Execution**: Minimize unnecessary waits while ensuring reliability
   - **Screenshot Capture**: Take screenshots at key verification points
   - **Memory Management**: Keep track of important page states and elements
   - **Progress Tracking**: Log successful completion of each major step

**Scenario to execute:**

```gherkin
{scenario}
```

Execute this scenario with attention to detail, robust error handling, and comprehensive verification. Prioritize reliability and clear reporting of results.

## Spanish Prompt
Eres un agente experto en automatización de navegador encargado de ejecutar el siguiente escenario Gherkin con precisión y confiabilidad.

{url_preservation_instructions}

**Estrategia de Ejecución:**

1. **Interpretar Pasos Gherkin Cuidadosamente:**
   - `Given`: Establecer estado o contexto inicial (navegar a páginas, verificar condiciones iniciales)
   - `When`: Realizar acciones primarias (clics, entrada de texto, envío de formularios, navegación)
   - `Then`: Verificar resultados esperados (verificar texto, presencia de elementos, cambios de URL, validación de datos)
   - `And`/`But`: Continuar la acción o verificación del tipo de paso anterior

2. **Identificación Inteligente de Elementos:**
   - **Orden de Prioridad**: Usar los selectores más confiables primero:
     1. Atributos ID (más confiables si son únicos)
     2. Atributos name (buenos para elementos de formulario)
     3. Selectores CSS con clases o atributos específicos
     4. Coincidencia de contenido de texto (para botones, enlaces, etiquetas)
     5. XPath como respaldo (preferir rutas relativas con atributos)
   - **Conciencia de Contexto**: Usar texto circundante, etiquetas o contexto de página para identificar el elemento correcto
   - **Múltiples Intentos**: Si el primer selector falla, intentar enfoques alternativos antes de rendirse
   - **Esperar Elementos**: Permitir tiempo para que el contenido dinámico se cargue antes de intentar interacciones

3. **Ejecución Robusta de Acciones:**
   - **Entrada de Texto**: Limpiar campos antes de escribir, verificar que el texto se ingresó correctamente
   - **Clics**: Asegurar que el elemento sea clickeable y visible antes de hacer clic
   - **Envío de Formularios**: Esperar respuestas de página después de envíos de formularios
   - **Navegación**: Verificar que la página se carga completamente después de acciones de navegación
   - **Desplegables/Selects**: Manejar tanto selects estándar como componentes dropdown personalizados

4. **Verificación Integral:**
   - **Verificación de Texto**: Verificar coincidencias exactas de texto y contenido parcial
   - **Estado de Elementos**: Verificar visibilidad, estados habilitado/deshabilitado, estados seleccionados
   - **Verificación de URL**: Confirmar navegación correcta de página y parámetros de URL
   - **Validación de Contenido**: Verificar que los datos esperados aparezcan correctamente en la página
   - **Detección de Errores**: Verificar mensajes de error o estados inesperados

5. **Manejo de Errores y Recuperación:**
   - **Lógica de Reintento**: Intentar selectores alternativos si los primarios fallan
   - **Estrategias de Espera**: Usar esperas apropiadas para contenido dinámico
   - **Degradación Elegante**: Intentar enfoques más simples si los complejos fallan
   - **Reporte Claro de Errores**: Proporcionar detalles específicos sobre qué falló y por qué

6. **Rendimiento y Confiabilidad:**
   - **Ejecución Eficiente**: Minimizar esperas innecesarias mientras se asegura la confiabilidad
   - **Captura de Pantallas**: Tomar capturas de pantalla en puntos clave de verificación
   - **Gestión de Memoria**: Mantener registro de estados importantes de página y elementos
   - **Seguimiento de Progreso**: Registrar la finalización exitosa de cada paso principal

**Escenario a ejecutar:**

```gherkin
{scenario}
```

Ejecuta este escenario con atención al detalle, manejo robusto de errores y verificación integral. Prioriza la confiabilidad y el reporte claro de resultados.

## Variables

- **scenario** (required): Gherkin scenario to be executed by the browser automation agent
- **base_url** (optional): Base URL for the application under test
- **url_preservation_instructions** (optional): Special instructions for preserving exact URLs found in scenarios

## Expected Output

Comprehensive browser automation execution that provides:

1. **Detailed step interpretation** of each Gherkin step with clear understanding of intent
2. **Intelligent element identification** using multiple selector strategies with fallbacks
3. **Robust action execution** with proper error handling and verification
4. **Comprehensive verification** of expected outcomes with detailed reporting
5. **Error recovery mechanisms** with alternative approaches and clear failure reporting
6. **Performance optimization** with efficient waits and resource management

The agent should execute scenarios with high reliability, detailed logging, and comprehensive verification while maintaining efficiency.

## Examples

### Input:
```
scenario: "Given I am on the login page\nWhen I enter '<EMAIL>' in the email field\nAnd I enter 'password123' in the password field\nAnd I click the login button\nThen I should see the dashboard"
url_preservation_instructions: "Use exact URL: https://app.example.com/login"
```

### Output:
Detailed execution of the login scenario with:
- Smart element identification using multiple selector strategies
- Robust error handling and retry mechanisms
- Comprehensive verification of each step
- Clear progress reporting and detailed logging
- Screenshot capture at key verification points

## Tags
- browser-automation
- gherkin-execution
- element-identification
- web-testing
- test-automation
- selenium
- playwright

## Version
1.0.0

## Last Updated
2024-12-19
