# Browser Automation Task Generation

## Description
Generates simple and direct browser automation instructions for executing Gherkin scenarios naturally.

## Purpose
This prompt generates straightforward browser automation instructions for executing Gherkin scenarios. It focuses on natural execution without overly detailed step-by-step breakdowns.

## Input Format
- **scenario**: Gherkin scenario text containing Given/When/Then/And/But steps
- **url_preservation_instructions**: Optional instructions for preserving specific URLs mentioned in scenarios
- **base_url**: Optional base URL for the application under test

## Output Format
Simple browser automation execution instructions that allow the agent to work naturally.

## English Prompt
You are a browser automation agent. Execute the following Gherkin scenario naturally and efficiently.

{url_preservation_instructions}

**Instructions:**
- Follow the Gherkin steps in order
- Use common sense to identify elements (by ID, name, text content, CSS selectors, etc.)
- Perform actions as described in the steps
- Verify outcomes as specified in Then steps
- If you encounter issues, try alternative approaches before failing

**Scenario to execute:**

```gherkin
{scenario}
```

Execute this scenario step by step. Work naturally and efficiently without generating detailed action plans.

## Spanish Prompt
Eres un agente de automatización de navegador. Ejecuta el siguiente escenario Gherkin de manera natural y eficiente.

{url_preservation_instructions}

**Instrucciones:**
- Sigue los pasos Gherkin en orden
- Usa sentido común para identificar elementos (por ID, nombre, contenido de texto, selectores CSS, etc.)
- Realiza las acciones como se describe en los pasos
- Verifica los resultados como se especifica en los pasos Then
- Si encuentras problemas, intenta enfoques alternativos antes de fallar

**Escenario a ejecutar:**

```gherkin
{scenario}
```

Ejecuta este escenario paso a paso. Trabaja de manera natural y eficiente sin generar planes de acción detallados.

## Variables

- **scenario** (required): Gherkin scenario to be executed by the browser automation agent
- **base_url** (optional): Base URL for the application under test
- **url_preservation_instructions** (optional): Special instructions for preserving exact URLs found in scenarios

## Expected Output

Natural browser automation execution that:

1. **Follows Gherkin steps** in order without over-analysis
2. **Uses intelligent element identification** with common selectors
3. **Performs actions** as described naturally
4. **Verifies outcomes** as specified in Then steps
5. **Handles errors** gracefully with alternative approaches

The agent should work efficiently and naturally without generating verbose action plans.

## Examples

### Input:
```
scenario: "Given I am on the login page\nWhen I enter '<EMAIL>' in the email field\nAnd I enter 'password123' in the password field\nAnd I click the login button\nThen I should see the dashboard"
url_preservation_instructions: "Use exact URL: https://app.example.com/login"
```

### Output:
Natural execution of the login scenario without detailed step-by-step breakdowns.

## Tags
- browser-automation
- gherkin-execution
- element-identification
- web-testing
- test-automation
- selenium
- playwright

## Version
1.0.0

## Last Updated
2024-12-19
