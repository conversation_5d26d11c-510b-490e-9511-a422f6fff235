"use client";

import { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Save, 
  ArrowLeft, 
  Eye, 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  FileText,
  Code,
  Settings,
  Play
} from 'lucide-react';
import Link from 'next/link';
import { fetchPromptDetail, updatePrompt, validatePrompt } from '@/lib/api';

interface PromptMetadata {
  title: string;
  description: string;
  version: string;
  lastModified: string;
  tags: string[];
  author?: string;
  language?: string;
}

interface PromptContent {
  [section: string]: string;
}

interface PromptDetail {
  category: string;
  prompt_id: string;
  metadata: PromptMetadata;
  content: PromptContent;
  validation?: {
    is_valid: boolean;
    errors: string[];
    warnings: string[];
  };
}

export default function PromptEditorPage() {
  const params = useParams();
  const router = useRouter();
  const category = params.category as string;
  const id = params.id as string;

  const [prompt, setPrompt] = useState<PromptDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [validating, setValidating] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const [activeTab, setActiveTab] = useState('content');
  const [validation, setValidation] = useState<any>(null);

  useEffect(() => {
    loadPrompt();
  }, [category, id]);

  const loadPrompt = async () => {
    try {
      setLoading(true);
      const data: PromptDetail = await fetchPromptDetail(category, id);
      setPrompt(data);
      
      // Load validation status
      await validatePromptData(data);
    } catch (error) {
      console.error('Error loading prompt:', error);
    } finally {
      setLoading(false);
    }
  };

  const validatePromptData = async (promptData?: PromptDetail) => {
    try {
      setValidating(true);
      const validationResult = await validatePrompt(category, id);
      setValidation(validationResult);
      
      if (promptData) {
        setPrompt(prev => prev ? { ...prev, validation: validationResult } : null);
      }
    } catch (error) {
      console.error('Error validating prompt:', error);
    } finally {
      setValidating(false);
    }
  };

  const savePrompt = async () => {
    if (!prompt) return;

    try {
      setSaving(true);
      await updatePrompt(category, id, {
        metadata: prompt.metadata,
        content: prompt.content
      });
      
      setHasChanges(false);
      await validatePromptData();
      
      // Show success message
      console.log('Prompt saved successfully');
    } catch (error) {
      console.error('Error saving prompt:', error);
    } finally {
      setSaving(false);
    }
  };

  const updateMetadata = (field: keyof PromptMetadata, value: any) => {
    if (!prompt) return;
    
    setPrompt(prev => ({
      ...prev!,
      metadata: {
        ...prev!.metadata,
        [field]: value
      }
    }));
    setHasChanges(true);
  };

  const updateContent = (section: string, value: string) => {
    if (!prompt) return;
    
    setPrompt(prev => ({
      ...prev!,
      content: {
        ...prev!.content,
        [section]: value
      }
    }));
    setHasChanges(true);
  };

  const addTag = (tag: string) => {
    if (!prompt || !tag.trim() || prompt.metadata.tags.includes(tag.trim())) return;
    
    updateMetadata('tags', [...prompt.metadata.tags, tag.trim()]);
  };

  const removeTag = (tagToRemove: string) => {
    if (!prompt) return;
    
    updateMetadata('tags', prompt.metadata.tags.filter(tag => tag !== tagToRemove));
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  if (!prompt) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-12">
          <XCircle className="h-12 w-12 text-red-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Prompt not found</h3>
          <p className="text-gray-600 mb-4">The requested prompt could not be loaded.</p>
          <Link href="/prompts">
            <Button>Back to Prompts</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <Link href="/prompts">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{prompt.metadata.title}</h1>
            <p className="text-gray-600 text-sm">
              {category} / {id}
            </p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          {/* Validation Status */}
          {validation && (
            <div className="flex items-center gap-2">
              {validation.is_valid ? (
                <CheckCircle className="h-5 w-5 text-green-500" />
              ) : (
                <XCircle className="h-5 w-5 text-red-500" />
              )}
              <span className="text-sm text-gray-600">
                {validation.is_valid ? 'Valid' : 'Has Issues'}
              </span>
            </div>
          )}
          
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => validatePromptData()}
            disabled={validating}
          >
            {validating ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600" />
            ) : (
              <Play className="h-4 w-4" />
            )}
            Validate
          </Button>
          
          <Link href={`/prompts/${category}/${id}/preview`}>
            <Button variant="outline" size="sm">
              <Eye className="h-4 w-4 mr-2" />
              Preview
            </Button>
          </Link>
          
          <Button 
            onClick={savePrompt}
            disabled={saving || !hasChanges}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {saving ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
            ) : (
              <Save className="h-4 w-4 mr-2" />
            )}
            Save Changes
          </Button>
        </div>
      </div>

      {/* Validation Alerts */}
      {validation && !validation.is_valid && (
        <Alert className="mb-6 border-red-200 bg-red-50">
          <XCircle className="h-4 w-4" />
          <AlertDescription>
            <div className="font-medium mb-2">Validation Issues:</div>
            <ul className="list-disc list-inside space-y-1">
              {validation.errors.map((error: string, index: number) => (
                <li key={index} className="text-sm">{error}</li>
              ))}
            </ul>
          </AlertDescription>
        </Alert>
      )}

      {validation && validation.warnings && validation.warnings.length > 0 && (
        <Alert className="mb-6 border-yellow-200 bg-yellow-50">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            <div className="font-medium mb-2">Warnings:</div>
            <ul className="list-disc list-inside space-y-1">
              {validation.warnings.map((warning: string, index: number) => (
                <li key={index} className="text-sm">{warning}</li>
              ))}
            </ul>
          </AlertDescription>
        </Alert>
      )}

      {/* Editor Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="content">
            <FileText className="h-4 w-4 mr-2" />
            Content
          </TabsTrigger>
          <TabsTrigger value="metadata">
            <Settings className="h-4 w-4 mr-2" />
            Metadata
          </TabsTrigger>
          <TabsTrigger value="raw">
            <Code className="h-4 w-4 mr-2" />
            Raw Markdown
          </TabsTrigger>
        </TabsList>

        {/* Content Editor */}
        <TabsContent value="content" className="space-y-6">
          {Object.entries(prompt.content).map(([section, content]) => (
            <Card key={section}>
              <CardHeader>
                <CardTitle className="text-lg capitalize">
                  {section.replace(/_/g, ' ')}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Textarea
                  value={content}
                  onChange={(e) => updateContent(section, e.target.value)}
                  className="min-h-[200px] font-mono text-sm"
                  placeholder={`Enter ${section} content...`}
                />
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        {/* Metadata Editor */}
        <TabsContent value="metadata">
          <Card>
            <CardHeader>
              <CardTitle>Prompt Metadata</CardTitle>
              <CardDescription>
                Configure the metadata for this prompt
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="title">Title</Label>
                  <Input
                    id="title"
                    value={prompt.metadata.title}
                    onChange={(e) => updateMetadata('title', e.target.value)}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="version">Version</Label>
                  <Input
                    id="version"
                    value={prompt.metadata.version}
                    onChange={(e) => updateMetadata('version', e.target.value)}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={prompt.metadata.description}
                  onChange={(e) => updateMetadata('description', e.target.value)}
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="author">Author</Label>
                <Input
                  id="author"
                  value={prompt.metadata.author || ''}
                  onChange={(e) => updateMetadata('author', e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label>Tags</Label>
                <div className="flex flex-wrap gap-2 mb-2">
                  {prompt.metadata.tags.map(tag => (
                    <Badge 
                      key={tag} 
                      variant="secondary" 
                      className="cursor-pointer hover:bg-red-100"
                      onClick={() => removeTag(tag)}
                    >
                      {tag} ×
                    </Badge>
                  ))}
                </div>
                <Input
                  placeholder="Add tag and press Enter"
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      addTag(e.currentTarget.value);
                      e.currentTarget.value = '';
                    }
                  }}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Raw Markdown */}
        <TabsContent value="raw">
          <Card>
            <CardHeader>
              <CardTitle>Raw Markdown</CardTitle>
              <CardDescription>
                View and edit the raw markdown representation
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Textarea
                value={generateRawMarkdown()}
                onChange={(e) => parseRawMarkdown(e.target.value)}
                className="min-h-[500px] font-mono text-sm"
                placeholder="Raw markdown content..."
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );

  function generateRawMarkdown(): string {
    if (!prompt) return '';
    
    let markdown = `---\n`;
    markdown += `title: "${prompt.metadata.title}"\n`;
    markdown += `description: "${prompt.metadata.description}"\n`;
    markdown += `version: "${prompt.metadata.version}"\n`;
    markdown += `lastModified: "${prompt.metadata.lastModified}"\n`;
    markdown += `tags: [${prompt.metadata.tags.map(t => `"${t}"`).join(', ')}]\n`;
    if (prompt.metadata.author) {
      markdown += `author: "${prompt.metadata.author}"\n`;
    }
    markdown += `---\n\n`;
    
    Object.entries(prompt.content).forEach(([section, content]) => {
      markdown += `## ${section.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}\n\n`;
      markdown += `${content}\n\n`;
    });
    
    return markdown;
  }

  function parseRawMarkdown(markdown: string): void {
    // Simple parser for demonstration
    // In a real implementation, you'd want a more robust markdown parser
    console.log('Parsing raw markdown (not implemented in demo):', markdown);
  }
}
