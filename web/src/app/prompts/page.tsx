"use client";

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  FileText, 
  Edit3, 
  Search, 
  Plus, 
  CheckCircle, 
  XCircle,
  AlertCircle,
  Download,
  Upload
} from 'lucide-react';
import Link from 'next/link';
import { fetchPrompts, validateAllPrompts } from '@/lib/api';

interface PromptMetadata {
  title: string;
  description: string;
  version: string;
  lastModified: string;
  tags: string[];
  author?: string;
}

interface PromptItem {
  id: string;
  metadata: PromptMetadata;
  filePath: string;
  isValid?: boolean;
  validationErrors?: string[];
}

interface PromptCategories {
  [category: string]: PromptItem[];
}

interface PromptListResponse {
  categories: PromptCategories;
  total_prompts: number;
}

export default function PromptsPage() {
  const [prompts, setPrompts] = useState<PromptCategories>({});
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [validationStatus, setValidationStatus] = useState<Record<string, boolean>>({});

  useEffect(() => {
    loadPrompts();
  }, []);

  const loadPrompts = async () => {
    try {
      setLoading(true);
      const data: PromptListResponse = await fetchPrompts();
      setPrompts(data.categories);
      
      // Load validation status for all prompts
      await loadValidationStatus();
    } catch (error) {
      console.error('Error loading prompts:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadValidationStatus = async () => {
    try {
      const validation = await validateAllPrompts();
      const status: Record<string, boolean> = {};
      
      Object.entries(validation.results).forEach(([key, result]: [string, any]) => {
        status[key] = result.is_valid;
      });
      
      setValidationStatus(status);
    } catch (error) {
      console.error('Error loading validation status:', error);
    }
  };

  const getFilteredPrompts = () => {
    let allPrompts: Array<PromptItem & { category: string }> = [];
    
    Object.entries(prompts).forEach(([category, items]) => {
      if (selectedCategory === 'all' || selectedCategory === category) {
        allPrompts.push(...items.map(item => ({ ...item, category })));
      }
    });

    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      allPrompts = allPrompts.filter(prompt => 
        prompt.metadata.title.toLowerCase().includes(term) ||
        prompt.metadata.description.toLowerCase().includes(term) ||
        prompt.metadata.tags.some(tag => tag.toLowerCase().includes(term))
      );
    }

    return allPrompts;
  };

  const getCategoryStats = (category: string) => {
    const items = prompts[category] || [];
    const validCount = items.filter(item => validationStatus[`${category}/${item.id}`]).length;
    return { total: items.length, valid: validCount };
  };

  const categories = Object.keys(prompts);
  const filteredPrompts = getFilteredPrompts();

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Prompt Management</h1>
          <p className="text-gray-600">
            Manage and edit your AI prompts for test generation and automation
          </p>
        </div>
        <div className="flex gap-2 mt-4 md:mt-0">
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export All
          </Button>
          <Button variant="outline" size="sm">
            <Upload className="h-4 w-4 mr-2" />
            Import
          </Button>
          <Button size="sm">
            <Plus className="h-4 w-4 mr-2" />
            New Prompt
          </Button>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search prompts by title, description, or tags..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Category Tabs */}
      <Tabs value={selectedCategory} onValueChange={setSelectedCategory} className="mb-6">
        <TabsList className="grid w-full grid-cols-auto overflow-x-auto">
          <TabsTrigger value="all" className="text-sm">
            All ({filteredPrompts.length})
          </TabsTrigger>
          {categories.map(category => {
            const stats = getCategoryStats(category);
            return (
              <TabsTrigger key={category} value={category} className="text-sm">
                {category.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())} 
                <Badge variant="secondary" className="ml-2 text-xs">
                  {stats.valid}/{stats.total}
                </Badge>
              </TabsTrigger>
            );
          })}
        </TabsList>
      </Tabs>

      {/* Prompts Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {filteredPrompts.map((prompt) => {
          const validationKey = `${prompt.category}/${prompt.id}`;
          const isValid = validationStatus[validationKey];
          
          return (
            <Card key={`${prompt.category}/${prompt.id}`} className="hover:shadow-lg transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-lg mb-1">{prompt.metadata.title}</CardTitle>
                    <CardDescription className="text-sm">
                      {prompt.metadata.description}
                    </CardDescription>
                  </div>
                  <div className="ml-2">
                    {isValid === true && (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    )}
                    {isValid === false && (
                      <XCircle className="h-5 w-5 text-red-500" />
                    )}
                    {isValid === undefined && (
                      <AlertCircle className="h-5 w-5 text-yellow-500" />
                    )}
                  </div>
                </div>
              </CardHeader>

              <CardContent>
                <div className="space-y-3">
                  {/* Category & Version */}
                  <div className="flex items-center justify-between text-sm text-gray-600">
                    <Badge variant="outline" className="text-xs">
                      {prompt.category}
                    </Badge>
                    <span>v{prompt.metadata.version}</span>
                  </div>

                  {/* Tags */}
                  {prompt.metadata.tags.length > 0 && (
                    <div className="flex flex-wrap gap-1">
                      {prompt.metadata.tags.slice(0, 3).map(tag => (
                        <Badge key={tag} variant="secondary" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                      {prompt.metadata.tags.length > 3 && (
                        <Badge variant="secondary" className="text-xs">
                          +{prompt.metadata.tags.length - 3}
                        </Badge>
                      )}
                    </div>
                  )}

                  {/* Last Modified */}
                  <div className="text-xs text-gray-500">
                    Modified: {new Date(prompt.metadata.lastModified).toLocaleDateString()}
                  </div>

                  {/* Actions */}
                  <div className="flex gap-2 pt-2">
                    <Link href={`/prompts/${prompt.category}/${prompt.id}`} className="flex-1">
                      <Button variant="outline" size="sm" className="w-full">
                        <Edit3 className="h-4 w-4 mr-2" />
                        Edit
                      </Button>
                    </Link>
                    <Link href={`/prompts/${prompt.category}/${prompt.id}/preview`}>
                      <Button variant="ghost" size="sm">
                        <FileText className="h-4 w-4" />
                      </Button>
                    </Link>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Empty State */}
      {filteredPrompts.length === 0 && (
        <div className="text-center py-12">
          <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No prompts found</h3>
          <p className="text-gray-600 mb-4">
            {searchTerm 
              ? `No prompts match "${searchTerm}"` 
              : "Get started by creating your first prompt"
            }
          </p>
          {!searchTerm && (
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Create Prompt
            </Button>
          )}
        </div>
      )}
    </div>
  );
}
