"""
Prompt Validator for AgentQA
Validates prompt files structure, variables, and translations
"""

import os
import json
import re
from typing import Dict, List, Optional, Any
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

class PromptValidator:
    """Validates prompt files and their structure."""
    
    def __init__(self, prompts_dir: str = "prompts"):
        """Initialize the validator.
        
        Args:
            prompts_dir: Directory containing prompt markdown files
        """
        self.prompts_dir = Path(prompts_dir)
        
        # Required sections for a valid prompt file
        self.required_sections = [
            "Purpose",
            "Input Format", 
            "Output Format",
            "English Prompt",
            "Variables",
            "Examples"
        ]
        
        # Optional but recommended sections
        self.optional_sections = [
            "Spanish Prompt",
            "Validation Rules",
            "Version History"
        ]
        
        # Supported languages
        self.supported_languages = ["en", "es"]
        
        # Pattern to match variables in format {variable_name}
        self.variable_pattern = re.compile(r'\{([a-zA-Z_][a-zA-Z0-9_]*)\}')
    
    def validate_all_prompts(self) -> Dict[str, Dict[str, Any]]:
        """Validate all prompts in the prompts directory.
        
        Returns:
            Dict with validation results for each category and prompt
        """
        results = {}
        
        if not self.prompts_dir.exists():
            return {"error": f"Prompts directory not found: {self.prompts_dir}"}
        
        for category_dir in self.prompts_dir.iterdir():
            if category_dir.is_dir() and category_dir.name != "shared":
                category_results = self.validate_category(category_dir.name)
                results[category_dir.name] = category_results
        
        return results
    
    def validate_category(self, category: str) -> Dict[str, Any]:
        """Validate all prompts in a specific category.
        
        Args:
            category: Category name to validate
            
        Returns:
            Dict with validation results for the category
        """
        category_path = self.prompts_dir / category
        results = {
            "valid": True,
            "errors": [],
            "warnings": [],
            "prompts": {}
        }
        
        if not category_path.exists():
            results["valid"] = False
            results["errors"].append(f"Category directory not found: {category_path}")
            return results
        
        # Validate metadata.json
        metadata_results = self.validate_metadata(category)
        if metadata_results["errors"]:
            results["errors"].extend(metadata_results["errors"])
            results["valid"] = False
        if metadata_results["warnings"]:
            results["warnings"].extend(metadata_results["warnings"])
        
        # Load metadata to get prompt list
        try:
            metadata_file = category_path / "metadata.json"
            if metadata_file.exists():
                with open(metadata_file, 'r', encoding='utf-8') as f:
                    metadata = json.load(f)
                
                # Validate each prompt defined in metadata
                for prompt_info in metadata.get("prompts", []):
                    prompt_id = prompt_info.get("id")
                    if prompt_id:
                        prompt_results = self.validate_prompt(category, prompt_id)
                        results["prompts"][prompt_id] = prompt_results
                        
                        if not prompt_results["valid"]:
                            results["valid"] = False
                            results["errors"].extend([f"{prompt_id}: {err}" for err in prompt_results["errors"]])
                        
                        if prompt_results["warnings"]:
                            results["warnings"].extend([f"{prompt_id}: {warn}" for warn in prompt_results["warnings"]])
        
        except Exception as e:
            results["valid"] = False
            results["errors"].append(f"Error validating category {category}: {e}")
        
        return results
    
    def validate_prompt(self, category: str, prompt_id: str) -> Dict[str, Any]:
        """Validate a specific prompt file.
        
        Args:
            category: Prompt category
            prompt_id: Prompt identifier
            
        Returns:
            Dict with validation results
        """
        results = {
            "valid": True,
            "errors": [],
            "warnings": []
        }
        
        try:
            # Load metadata to find the prompt file
            metadata_file = self.prompts_dir / category / "metadata.json"
            if not metadata_file.exists():
                results["errors"].append("metadata.json not found")
                results["valid"] = False
                return results
            
            with open(metadata_file, 'r', encoding='utf-8') as f:
                metadata = json.load(f)
            
            # Find prompt info in metadata
            prompt_info = None
            for prompt in metadata.get("prompts", []):
                if prompt.get("id") == prompt_id:
                    prompt_info = prompt
                    break
            
            if not prompt_info:
                results["errors"].append(f"Prompt '{prompt_id}' not found in metadata")
                results["valid"] = False
                return results
            
            # Validate the markdown file
            file_path = self.prompts_dir / category / prompt_info["file"]
            if not file_path.exists():
                results["errors"].append(f"Prompt file not found: {file_path}")
                results["valid"] = False
                return results
            
            # Parse and validate file structure
            structure_results = self.validate_file_structure(str(file_path))
            if structure_results["errors"]:
                results["errors"].extend(structure_results["errors"])
                results["valid"] = False
            if structure_results["warnings"]:
                results["warnings"].extend(structure_results["warnings"])
            
            # Validate variables consistency
            if structure_results["sections"]:
                variables_results = self.validate_variables_consistency(structure_results["sections"])
                if variables_results["errors"]:
                    results["errors"].extend(variables_results["errors"])
                    results["valid"] = False
                if variables_results["warnings"]:
                    results["warnings"].extend(variables_results["warnings"])
                
                # Validate translations
                translations_results = self.validate_translations(structure_results["sections"])
                if translations_results["errors"]:
                    results["errors"].extend(translations_results["errors"])
                    results["valid"] = False
                if translations_results["warnings"]:
                    results["warnings"].extend(translations_results["warnings"])
        
        except Exception as e:
            results["valid"] = False
            results["errors"].append(f"Validation error: {e}")
        
        return results
    
    def validate_file_structure(self, file_path: str) -> Dict[str, Any]:
        """Validate the structure of a markdown prompt file.
        
        Args:
            file_path: Path to the markdown file
            
        Returns:
            Dict with validation results and parsed sections
        """
        results = {
            "valid": True,
            "errors": [],
            "warnings": [],
            "sections": {}
        }
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Parse markdown sections
            from src.Core.prompt_markdown_parser import PromptMarkdownParser
            parser = PromptMarkdownParser()
            sections = parser.parse_markdown_content(content, file_path)
            results["sections"] = sections
            
            # Check for required sections
            for required_section in self.required_sections:
                if required_section not in sections:
                    results["errors"].append(f"Missing required section: {required_section}")
                    results["valid"] = False
                elif not sections[required_section].strip():
                    results["errors"].append(f"Required section is empty: {required_section}")
                    results["valid"] = False
            
            # Check for recommended sections
            for optional_section in self.optional_sections:
                if optional_section not in sections:
                    results["warnings"].append(f"Missing recommended section: {optional_section}")
            
            # Validate that title exists
            if not content.strip().startswith('#'):
                results["errors"].append("File should start with a title (# Title)")
                results["valid"] = False
        
        except Exception as e:
            results["valid"] = False
            results["errors"].append(f"Error reading file: {e}")
        
        return results
    
    def validate_variables_consistency(self, sections: Dict[str, str]) -> Dict[str, Any]:
        """Validate that variables are consistent across sections.
        
        Args:
            sections: Parsed sections from markdown file
            
        Returns:
            Dict with validation results
        """
        results = {
            "valid": True,
            "errors": [],
            "warnings": []
        }
        
        try:
            # Extract variables from English prompt
            english_vars = set()
            if "English Prompt" in sections:
                english_vars = set(self.variable_pattern.findall(sections["English Prompt"]))
            
            # Extract variables from Spanish prompt if it exists
            spanish_vars = set()
            if "Spanish Prompt" in sections:
                spanish_vars = set(self.variable_pattern.findall(sections["Spanish Prompt"]))
            
            # Check consistency between English and Spanish
            if spanish_vars and english_vars != spanish_vars:
                missing_in_spanish = english_vars - spanish_vars
                missing_in_english = spanish_vars - english_vars
                
                if missing_in_spanish:
                    results["errors"].append(f"Variables missing in Spanish prompt: {missing_in_spanish}")
                    results["valid"] = False
                
                if missing_in_english:
                    results["errors"].append(f"Variables missing in English prompt: {missing_in_english}")
                    results["valid"] = False
            
            # Check that all variables are documented in Variables section
            if "Variables" in sections and english_vars:
                variables_section = sections["Variables"]
                documented_vars = set(self.variable_pattern.findall(variables_section))
                
                # Also check for markdown-style variable documentation
                var_doc_pattern = re.compile(r'`\{([a-zA-Z_][a-zA-Z0-9_]*)\}`')
                documented_vars.update(var_doc_pattern.findall(variables_section))
                
                undocumented_vars = english_vars - documented_vars
                if undocumented_vars:
                    results["warnings"].append(f"Variables not documented in Variables section: {undocumented_vars}")
            
            # Check for unused documented variables
            if "Variables" in sections:
                variables_section = sections["Variables"]
                var_doc_pattern = re.compile(r'`\{([a-zA-Z_][a-zA-Z0-9_]*)\}`')
                documented_vars = set(var_doc_pattern.findall(variables_section))
                
                unused_vars = documented_vars - english_vars
                if unused_vars:
                    results["warnings"].append(f"Documented variables not used in prompts: {unused_vars}")
        
        except Exception as e:
            results["valid"] = False
            results["errors"].append(f"Error validating variables: {e}")
        
        return results
    
    def validate_translations(self, sections: Dict[str, str]) -> Dict[str, Any]:
        """Validate translation completeness and quality.
        
        Args:
            sections: Parsed sections from markdown file
            
        Returns:
            Dict with validation results
        """
        results = {
            "valid": True,
            "errors": [],
            "warnings": []
        }
        
        try:
            # Check if Spanish translation exists
            if "Spanish Prompt" not in sections:
                results["warnings"].append("No Spanish translation provided")
            else:
                spanish_content = sections["Spanish Prompt"].strip()
                if not spanish_content:
                    results["warnings"].append("Spanish Prompt section is empty")
                
                # Basic quality checks
                english_content = sections.get("English Prompt", "").strip()
                if english_content and spanish_content:
                    # Check if Spanish is just a copy of English (simple heuristic)
                    if spanish_content == english_content:
                        results["warnings"].append("Spanish prompt appears to be identical to English")
                    
                    # Check for common English words that might indicate incomplete translation
                    english_indicators = ['the', 'and', 'that', 'this', 'with', 'for', 'are', 'you']
                    spanish_lower = spanish_content.lower()
                    found_english = [word for word in english_indicators if f' {word} ' in spanish_lower]
                    if found_english:
                        results["warnings"].append(f"Spanish prompt may contain English words: {found_english}")
        
        except Exception as e:
            results["valid"] = False
            results["errors"].append(f"Error validating translations: {e}")
        
        return results
    
    def validate_metadata(self, category: str) -> Dict[str, Any]:
        """Validate metadata.json file for a category.
        
        Args:
            category: Category name
            
        Returns:
            Dict with validation results
        """
        results = {
            "valid": True,
            "errors": [],
            "warnings": []
        }
        
        try:
            metadata_file = self.prompts_dir / category / "metadata.json"
            if not metadata_file.exists():
                results["errors"].append("metadata.json file not found")
                results["valid"] = False
                return results
            
            with open(metadata_file, 'r', encoding='utf-8') as f:
                metadata = json.load(f)
            
            # Check required fields
            required_fields = ["category", "version", "prompts"]
            for field in required_fields:
                if field not in metadata:
                    results["errors"].append(f"Missing required field in metadata: {field}")
                    results["valid"] = False
            
            # Validate prompts array
            if "prompts" in metadata:
                prompts = metadata["prompts"]
                if not isinstance(prompts, list):
                    results["errors"].append("'prompts' should be an array")
                    results["valid"] = False
                else:
                    for i, prompt in enumerate(prompts):
                        if not isinstance(prompt, dict):
                            results["errors"].append(f"Prompt {i} should be an object")
                            results["valid"] = False
                            continue
                        
                        # Check required prompt fields
                        required_prompt_fields = ["id", "name", "description", "file", "languages"]
                        for field in required_prompt_fields:
                            if field not in prompt:
                                results["errors"].append(f"Prompt {i} missing required field: {field}")
                                results["valid"] = False
                        
                        # Check that file exists
                        if "file" in prompt:
                            file_path = self.prompts_dir / category / prompt["file"]
                            if not file_path.exists():
                                results["errors"].append(f"Prompt file not found: {prompt['file']}")
                                results["valid"] = False
                        
                        # Validate languages
                        if "languages" in prompt:
                            languages = prompt["languages"]
                            if not isinstance(languages, list):
                                results["errors"].append(f"Prompt {i} 'languages' should be an array")
                                results["valid"] = False
                            else:
                                for lang in languages:
                                    if lang not in self.supported_languages:
                                        results["warnings"].append(f"Unsupported language in prompt {i}: {lang}")
            
            # Check category consistency
            if "category" in metadata and metadata["category"] != category:
                results["warnings"].append(f"Category in metadata ({metadata['category']}) doesn't match directory name ({category})")
        
        except json.JSONDecodeError as e:
            results["valid"] = False
            results["errors"].append(f"Invalid JSON in metadata.json: {e}")
        except Exception as e:
            results["valid"] = False
            results["errors"].append(f"Error validating metadata: {e}")
        
        return results
    
    def get_validation_summary(self, results: Dict[str, Any]) -> str:
        """Generate a human-readable validation summary.
        
        Args:
            results: Validation results from validate_all_prompts()
            
        Returns:
            Formatted summary string
        """
        summary = []
        summary.append("🔍 Prompt Validation Summary")
        summary.append("=" * 50)
        
        if "error" in results:
            summary.append(f"❌ {results['error']}")
            return "\n".join(summary)
        
        total_categories = len(results)
        valid_categories = sum(1 for cat_results in results.values() if cat_results.get("valid", False))
        
        summary.append(f"📊 Categories: {valid_categories}/{total_categories} valid")
        summary.append("")
        
        for category, cat_results in results.items():
            status = "✅" if cat_results.get("valid", False) else "❌"
            summary.append(f"{status} {category}")
            
            if cat_results.get("errors"):
                for error in cat_results["errors"]:
                    summary.append(f"   ❌ {error}")
            
            if cat_results.get("warnings"):
                for warning in cat_results["warnings"]:
                    summary.append(f"   ⚠️  {warning}")
            
            # Show prompt-level results
            prompts = cat_results.get("prompts", {})
            if prompts:
                valid_prompts = sum(1 for p in prompts.values() if p.get("valid", False))
                summary.append(f"   📝 Prompts: {valid_prompts}/{len(prompts)} valid")
            
            summary.append("")
        
        return "\n".join(summary)
